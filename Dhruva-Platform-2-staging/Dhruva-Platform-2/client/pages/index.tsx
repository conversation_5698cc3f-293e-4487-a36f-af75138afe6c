import { Box, Text, Center, Button } from "@chakra-ui/react";
import Head from "next/head";

export default function RootPage() {

  return (
    <>
      <Head>
        <title>404 - Page Not Found</title>
      </Head>
      <Center h="100vh">
        <Box textAlign="center">
          <Text fontSize="6xl" fontWeight="bold" color="gray.400">
            404
          </Text>
          <Text fontSize="xl" color="gray.500" mb={4}>
            Page Not Found
          </Text>
          <Text fontSize="md" color="gray.400" mb={8}>
            The application is not available at this path.
          </Text>
          <Button
            colorScheme="orange"
            onClick={() => window.location.href = '/dhruva'}
          >
            Go to Dhruva Platform
          </Button>
        </Box>
      </Center>
    </>
  );
}
