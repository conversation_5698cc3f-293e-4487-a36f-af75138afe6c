import Image from "next/image";
import { ImageProps } from "next/image";

/**
 * Base Image component that handles base path automatically
 * This component wraps Next.js Image and ensures proper asset loading with base path
 */
interface BaseImageProps extends Omit<ImageProps, 'src'> {
  src: string;
}

const BaseImage: React.FC<BaseImageProps> = ({ src, ...props }) => {
  // For Next.js with basePath, we need to ensure the src starts with /
  // Next.js will automatically prepend the basePath for static assets
  const normalizedSrc = src.startsWith('/') ? src : `/${src}`;

  return <Image src={normalizedSrc} {...props} />;
};

export default BaseImage;
