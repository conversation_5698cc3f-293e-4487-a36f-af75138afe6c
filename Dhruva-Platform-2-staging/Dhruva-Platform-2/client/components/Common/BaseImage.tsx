import Image from "next/image";
import { ImageProps } from "next/image";

/**
 * Base Image component that handles base path automatically
 * This component wraps Next.js Image and ensures proper asset loading with base path
 */
interface BaseImageProps extends Omit<ImageProps, 'src'> {
  src: string;
}

const BaseImage: React.FC<BaseImageProps> = ({ src, ...props }) => {
  // Next.js automatically handles the base path for static assets
  // when using the Image component, so we don't need to manually add the prefix
  return <Image src={src} {...props} />;
};

export default BaseImage;
