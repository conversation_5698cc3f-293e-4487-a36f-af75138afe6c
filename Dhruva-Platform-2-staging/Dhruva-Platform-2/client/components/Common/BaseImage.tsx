import Image from "next/image";
import { ImageProps } from "next/image";

/**
 * Base Image component that handles base path automatically
 * This component wraps Next.js Image and ensures proper asset loading with base path
 */
interface BaseImageProps extends Omit<ImageProps, 'src'> {
  src: string;
}

const BaseImage: React.FC<BaseImageProps> = ({ src, ...props }) => {
  // For Next.js with basePath, we need to handle static assets correctly
  let imageSrc = src;

  // Ensure the src starts with /
  if (!imageSrc.startsWith('/')) {
    imageSrc = `/${imageSrc}`;
  }

  // Next.js should handle the basePath automatically, but if it doesn't work,
  // we can manually add it. Let's try without manual prefix first.
  return <Image src={imageSrc} {...props} />;
};

export default BaseImage;
