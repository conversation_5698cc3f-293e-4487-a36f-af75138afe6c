import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // If user is accessing the root path without /dhruva, redirect to /dhruva
  if (request.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/dhruva', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match only the root path for redirect
     */
    '/',
  ],
}
