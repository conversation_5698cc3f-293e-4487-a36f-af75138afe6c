/**
 * Utility functions for handling base path in the application
 */

// Get the base path from Next.js config
export const BASE_PATH = '/dhruva';

/**
 * Get the full asset path with base path prefix
 * @param assetPath - The asset path starting with /
 * @returns The full path with base path prefix
 */
export function getAssetPath(assetPath: string): string {
  // In development, Next.js handles this automatically
  // In production with basePath, we need to add the prefix
  if (process.env.NODE_ENV === 'production') {
    return `${BASE_PATH}${assetPath}`;
  }
  return assetPath;
}

/**
 * Get the pathname without base path for comparison
 * @param pathname - The full pathname
 * @returns The pathname without base path
 */
export function getPathnameWithoutBase(pathname: string): string {
  if (pathname.startsWith(BASE_PATH)) {
    return pathname.replace(BASE_PATH, '') || '/';
  }
  return pathname;
}

/**
 * Check if current path matches a route (handles base path)
 * @param currentPath - Current router pathname
 * @param targetPath - Target path to match
 * @returns Whether the paths match
 */
export function pathMatches(currentPath: string, targetPath: string): boolean {
  const cleanPath = getPathnameWithoutBase(currentPath);
  return cleanPath.includes(targetPath);
}
